import { NextResponse } from 'next/server';
import { v2 as cloudinary } from 'cloudinary';

export async function POST() {
  try {
    // Validate environment variables
    const cloudName = process.env.CLOUDINARY_CLOUD_NAME;
    const apiKey = process.env.CLOUDINARY_API_KEY;
    const apiSecret = process.env.CLOUDINARY_API_SECRET;

    if (!cloudName || !apiKey || !apiSecret) {
      console.error('Missing Cloudinary environment variables:', {
        cloudName: !!cloudName,
        apiKey: !!apiKey,
        apiSecret: !!apiSecret,
      });
      return NextResponse.json(
        { error: 'Cloudinary configuration is incomplete' },
        { status: 500 }
      );
    }

    // Configure Cloudinary
    cloudinary.config({
      cloud_name: cloudName,
      api_key: apiKey,
      api_secret: apiSecret,
    });

    // Generate signed upload parameters
    const timestamp = Math.round(new Date().getTime() / 1000);

    // Parameters to be signed (excluding file, cloud_name, resource_type)
    // Note: api_key should NOT be included in signature according to Cloudinary docs
    const paramsToSign = {
      folder: 'utsav-footwear',
      timestamp: timestamp,
    };

    // Generate signature using Cloudinary's utility
    const signature = cloudinary.utils.api_sign_request(
      paramsToSign,
      apiSecret
    );

    // Return the signed upload parameters
    return NextResponse.json({
      signature,
      timestamp,
      api_key: apiKey,
      cloud_name: cloudName,
      folder: 'utsav-footwear',
    });
  } catch (error) {
    console.error('Error generating Cloudinary signature:', error);
    return NextResponse.json(
      { error: 'Failed to generate upload signature' },
      { status: 500 }
    );
  }
}

import Link from 'next/link';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: "Page Not Found - Utsav Footwear",
  description: "The page you're looking for doesn't exist. Browse our collection of premium Kolhapuri chappals or return to our homepage.",
  robots: {
    index: false,
    follow: false,
  },
};

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full text-center px-4">
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-gray-300">404</h1>
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            Page Not Found
          </h2>
          <p className="text-gray-600 mb-8">
            Sorry, we couldn&apos;t find the page you&apos;re looking for. 
            Perhaps you&apos;d like to browse our collection of premium Kolhapuri chappals?
          </p>
        </div>
        
        <div className="space-y-4">
          <Link
            href="/"
            className="block w-full bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold py-3 px-6 rounded-lg transition-colors duration-300"
          >
            Go to Homepage
          </Link>
          
          <Link
            href="/products"
            className="block w-full border-2 border-yellow-400 text-yellow-600 hover:bg-yellow-400 hover:text-gray-900 font-semibold py-3 px-6 rounded-lg transition-colors duration-300"
          >
            Browse Products
          </Link>
          
          <Link
            href="/contact"
            className="block w-full text-gray-600 hover:text-gray-900 font-medium py-2 transition-colors duration-300"
          >
            Contact Us
          </Link>
        </div>
        
        <div className="mt-8 pt-8 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Need help? <Link href="/contact" className="text-yellow-600 hover:text-yellow-700">Get in touch</Link> with our team.
          </p>
        </div>
      </div>
    </div>
  );
}

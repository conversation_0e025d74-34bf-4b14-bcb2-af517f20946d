// Structured Data (Schema.org) for SEO

export const organizationSchema = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Utsav Footwear",
  "description": "Premium quality Kolhapuri chappals and traditional footwear in Kolhapur. Handmade leather sandals for men, women, and kids.",
  "url": "https://utsavfootwear.com",
  "logo": "https://utsavfootwear.com/Ustav Logo-03.png",
  "contactPoint": {
    "@type": "ContactPoint",
    "contactType": "customer service",
    "areaServed": "IN",
    "availableLanguage": ["English", "Hindi", "Marathi"]
  },
  "sameAs": [
    // Add your social media URLs here when available
    // "https://www.facebook.com/utsavfootwear",
    // "https://www.instagram.com/utsavfootwear"
  ]
};

export const localBusinessSchema = {
  "@context": "https://schema.org",
  "@type": "LocalBusiness",
  "@id": "https://utsavfootwear.com",
  "name": "Utsav Footwear",
  "description": "Premium quality Kolhapuri chappals and traditional footwear in Kolhapur. Handmade leather sandals for men, women, and kids.",
  "url": "https://utsavfootwear.com",
  "telephone": "+91-XXXXXXXXXX", // Add actual phone number
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "Your Street Address", // Add actual address
    "addressLocality": "Kolhapur",
    "addressRegion": "Maharashtra",
    "postalCode": "416001", // Add actual postal code
    "addressCountry": "IN"
  },
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": "16.7050", // Kolhapur coordinates
    "longitude": "74.2433"
  },
  "openingHoursSpecification": [
    {
      "@type": "OpeningHoursSpecification",
      "dayOfWeek": [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday"
      ],
      "opens": "10:00",
      "closes": "20:00"
    }
  ],
  "priceRange": "₹₹",
  "paymentAccepted": ["Cash", "Credit Card", "UPI"],
  "currenciesAccepted": "INR"
};

export const websiteSchema = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "Utsav Footwear",
  "url": "https://utsavfootwear.com",
  "description": "Premium quality Kolhapuri chappals and traditional footwear in Kolhapur",
  "publisher": {
    "@type": "Organization",
    "name": "Utsav Footwear"
  },
  "potentialAction": {
    "@type": "SearchAction",
    "target": "https://utsavfootwear.com/products?search={search_term_string}",
    "query-input": "required name=search_term_string"
  }
};

export const breadcrumbSchema = (items: Array<{name: string, url: string}>) => ({
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": items.map((item, index) => ({
    "@type": "ListItem",
    "position": index + 1,
    "name": item.name,
    "item": `https://utsavfootwear.com${item.url}`
  }))
});

export const productSchema = (product: {
  name: string;
  description: string;
  image: string;
  price?: string;
  availability?: string;
}) => ({
  "@context": "https://schema.org",
  "@type": "Product",
  "name": product.name,
  "description": product.description,
  "image": product.image,
  "brand": {
    "@type": "Brand",
    "name": "Utsav Footwear"
  },
  "category": "Footwear",
  "offers": {
    "@type": "Offer",
    "price": product.price || "Contact for price",
    "priceCurrency": "INR",
    "availability": product.availability || "https://schema.org/InStock",
    "seller": {
      "@type": "Organization",
      "name": "Utsav Footwear"
    }
  }
});

export const faqSchema = (faqs: Array<{question: string, answer: string}>) => ({
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": faqs.map(faq => ({
    "@type": "Question",
    "name": faq.question,
    "acceptedAnswer": {
      "@type": "Answer",
      "text": faq.answer
    }
  }))
});

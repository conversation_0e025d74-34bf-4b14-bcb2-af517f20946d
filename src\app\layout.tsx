import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Navbar from "uf/components/Navbar";
import Footer from "uf/components/Footer";
import ScrollToTop from "uf/components/ScrollToTop";
import { GoogleAnalytics } from "uf/components/Analytics";
import WhatsAppButton from "uf/components/WhatsAppButton";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: "Utsav Footwear - Premium Kolhapuri Chappals in Kolhapur",
    template: "%s | Utsav Footwear"
  },
  description: "Premium quality Kolhapuri chappals and traditional footwear in Kolhapur. Handmade leather sandals for men, women, and kids. Top Kolhapuri chappal shop in Kolhapur.",
  keywords: "footwear, shoes, quality shoes, comfortable shoes, stylish shoes, Utsav Footwear, kolhapuri chappals, kolhapur, Kolhapuri sandals, Buy Kolhapuri chappals, Handmade leather sandals, Top Kolhapuri chappal in kolhapur, Kolhapuri chappal market in Kolhapur, kolhapuri chappal for women, kolhapuri chappal for men, kolhapuri chappal for boys, kolhapuri chappal for girls, kolhapuri chappal for kids, kolhapuri chappal in kolhapur, kolhapuri chappals prada, कोल्हापुरी चप्पल, kolhapuri chappal shop near me",
  authors: [{ name: "Utsav Footwear" }],
  creator: "Utsav Footwear",
  publisher: "Utsav Footwear",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://utsavfootwear.com",
    siteName: "Utsav Footwear",
    title: "Utsav Footwear - Premium Kolhapuri Chappals in Kolhapur",
    description: "Premium quality Kolhapuri chappals and traditional footwear in Kolhapur. Handmade leather sandals for men, women, and kids.",
    images: [
      {
        url: "/Ustav Logo-03.png",
        width: 1200,
        height: 630,
        alt: "Utsav Footwear - Premium Kolhapuri Chappals",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Utsav Footwear - Premium Kolhapuri Chappals in Kolhapur",
    description: "Premium quality Kolhapuri chappals and traditional footwear in Kolhapur. Handmade leather sandals for men, women, and kids.",
    images: ["/Ustav Logo-03.png"],
  },
  verification: {
    google: "your-google-verification-code", // Add your Google Search Console verification code
  },
  alternates: {
    canonical: "https://utsavfootwear.com",
  },
  category: "shopping",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="manifest" href="/manifest.json" />
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link
          rel="preload"
          as="image"
          href="/kolhapuribg.png"
        />
        <meta name="theme-color" content="#fbbf24" />
        <meta name="msapplication-TileColor" content="#fbbf24" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
        <GoogleAnalytics />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <div className="flex flex-col min-h-screen">
          <Navbar />
          <main className="flex-grow">
            {children}
          </main>
          <Footer />
          <ScrollToTop />
          <WhatsAppButton />
        </div>
      </body>
    </html>
  );
}

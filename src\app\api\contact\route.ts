import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: Request) {
  try {
    const { name, email, phone, subject, message } = await request.json();

    // Create transporter
    const transporter = nodemailer.createTransport({
      service: 'gmail',  // or your preferred email service
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    // Email template
    const mailOptions = {
    from: process.env.EMAIL_USER,
    to: process.env.EMAIL_RECIPIENT,
    replyTo: email,
    subject: `New message from utsav footwear for: ${subject}`,
    html: `
        <h3>Hello Utsav Footwear, you have a new message from: ${name}</h3>
        <p><strong>Person's email:</strong> ${email}</p>
        <p><strong>Person's phone:</strong> ${phone || 'Not provided'}</p>
        <p><strong>Why person is contacting:</strong> ${subject}</p>
        <h3>Person Message:</h3>
        <blockquote style="
        margin: 10px 0; 
        padding: 10px 15px; 
        border-left: 4px solid #ccc; 
        background-color: #f9f9f9;
        font-style: bold;
        ">
        ${message.replace(/\n/g, '<br>')}
        </blockquote>
    `,
    };


    // Send email
    await transporter.sendMail(mailOptions);

    return NextResponse.json(
      { message: 'Email sent successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { message: 'Failed to send email' },
      { status: 500 }
    );
  }
}

import { Metadata } from 'next';

export const metadata: Metadata = {
  title: "Contact Utsav Footwear - Get in Touch for Kolhapuri Chappals",
  description: "Contact Utsav Footwear for inquiries about our Kolhapuri chappals and traditional footwear. Visit our store in Kolhapur or get in touch for product information.",
  keywords: "contact utsav footwear, kolhapuri chappal store contact, footwear store kolhapur, get in touch, store location, customer service",
  openGraph: {
    title: "Contact Utsav Footwear - Get in Touch",
    description: "Contact Utsav Footwear for inquiries about our Kolhapuri chappals and traditional footwear. Visit our store in Kolhapur.",
    url: "https://utsavfootwear.com/contact",
    images: [
      {
        url: "/Ustav Logo-03.png",
        width: 1200,
        height: 630,
        alt: "Contact Utsav Footwear - Kolhapuri Chappal Store",
      },
    ],
  },
  alternates: {
    canonical: "https://utsavfootwear.com/contact",
  },
};

export default function ContactLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {/* Structured Data for Contact Page */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "ContactPage",
            "name": "Contact Utsav Footwear",
            "description": "Contact Utsav Footwear for inquiries about our Kolhapuri chappals and traditional footwear",
            "url": "https://utsavfootwear.com/contact",
            "mainEntity": {
              "@type": "LocalBusiness",
              "name": "Utsav Footwear",
              "address": {
                "@type": "PostalAddress",
                "addressLocality": "Kolhapur",
                "addressRegion": "Maharashtra",
                "addressCountry": "IN"
              }
            },
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": "https://utsavfootwear.com"
                },
                {
                  "@type": "ListItem",
                  "position": 2,
                  "name": "Contact",
                  "item": "https://utsavfootwear.com/contact"
                }
              ]
            }
          })
        }}
      />
      {children}
    </>
  );
}

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { productService, Product } from 'uf/lib/productService';
import ProductCard from 'uf/components/ProductCard';
import PageTransition from 'uf/components/PageTransition';
import AnimatedSection from 'uf/components/AnimatedSection';
import LoadingSpinner from 'uf/components/LoadingSpinner';


const PAGE_SIZES = [12, 24, 48, 96];

export default function Products() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [totalPages, setTotalPages] = useState(1);

  const fetchProducts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const { products: paginatedProducts, total } = await productService.getPaginatedProducts(currentPage, pageSize);
      setProducts(paginatedProducts);
      setTotalPages(Math.ceil(total / pageSize));
    } catch (err) {
      console.error('Error fetching products:', err);
      setError('Failed to load products. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize]);

  // Fetch products when page or page size changes
  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === 'ArrowLeft' && currentPage > 1) {
        setCurrentPage(prev => prev - 1);
      } else if (event.key === 'ArrowRight' && currentPage < totalPages) {
        setCurrentPage(prev => prev + 1);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentPage, totalPages]);

  if (loading) {
    return (
      <PageTransition className="min-h-screen py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <LoadingSpinner size="lg" text="Loading products..." />
        </div>
      </PageTransition>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
            <button
              onClick={fetchProducts}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <PageTransition className="min-h-screen py-12 relative bg-[url('/KCbg1.png')] bg-cover bg-center bg-no-repeat py-10">
      <div className="absolute inset-0 bg-black/40 z-0"></div>
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 ">
        {/* Header */}
        <AnimatedSection className="text-center mb-12" disableScrollAnimation={true}>
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Our Products
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Discover our kolahpuri chappal collection and quality footwear&apos;s.<br></br>
            Each pair is selected for comfort, style, and durability.
          </p>
        </AnimatedSection>

        {/* Products Grid */}
        {products.length === 0 ? (
          <AnimatedSection className="text-center py-16" disableScrollAnimation={true}>
            <div className="bg-gray-100 w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-6 transform transition-transform duration-300 hover:scale-110">
              <span className="text-4xl">👟</span>
            </div>
            <h3 className="text-2xl font-semibold text-white mb-4">No Products Yet</h3>
            <p className="text-gray-600 max-w-md mx-auto">
              We&apos;re currently updating our product gallery. Please check back soon
              to see our latest collection of quality footwear.
            </p>
          </AnimatedSection>
        ) : (
          <AnimatedSection disableScrollAnimation={true}>
            {/* Page Size Selector */}
            <div className="flex justify-end mb-4">
              <select
                value={pageSize}
                onChange={(e) => {
                  setPageSize(Number(e.target.value));
                  setCurrentPage(1);
                }}
                className="bg-white text-gray-700 px-4 py-2 rounded-lg shadow border border-gray-200"
              >
                {PAGE_SIZES.map(size => (
                  <option key={size} value={size}>
                    Show {size} per page
                  </option>
                ))}
              </select>
            </div>

            {/* Products Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {products.map((product) => (
                  <ProductCard
                    key={product.id}
                    id={product.id!}
                    imageUrl={product.imageUrl}
                    caption={product.caption}
                    name={product.name}
                  isAdmin={false}
                />
              ))}
            </div>

            {/* Pagination Controls */}
            {totalPages > 1 && (
              <div className="mt-8 flex justify-center items-center space-x-4">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-4 py-2 bg-white text-gray-700 rounded-lg shadow border border-gray-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
                  aria-label="Previous page"
                >
                  ←
                </button>
                
                <div className="flex items-center space-x-2">
                  {Array.from({ length: totalPages }, (_, i) => i + 1)
                    .filter(page => {
                      // Show first page, last page, current page, and pages around current page
                      return (
                        page === 1 ||
                        page === totalPages ||
                        Math.abs(page - currentPage) <= 1
                      );
                    })
                    .map((page, index, array) => (
                      <React.Fragment key={page}>
                        {index > 0 && array[index - 1] !== page - 1 && (
                          <span className="text-white">...</span>
                        )}
                        <button
                          onClick={() => setCurrentPage(page)}
                          className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                            currentPage === page
                              ? 'bg-blue-500 text-white'
                              : 'bg-white text-gray-700 hover:bg-gray-50'
                          } border border-gray-200 shadow transition-colors`}
                        >
                          {page}
                        </button>
                      </React.Fragment>
                    ))}
                </div>

                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-4 py-2 bg-white text-gray-700 rounded-lg shadow border border-gray-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
                  aria-label="Next page"
                >
                  →
                </button>
              </div>
            )}

            {/* Keyboard Navigation Hint */}
            {totalPages > 1 && (
              <div className="mt-4 text-center text-white text-sm">
                Use ← and → arrow keys to navigate between pages
              </div>
            )}
          </AnimatedSection>
        )}

        {/* Call to Action */}
        <AnimatedSection className="mt-16 text-center" delay={0.3} disableScrollAnimation={true}>
          <div className="bg-blue-50 rounded-lg p-8 transform transition-transform duration-300 hover:scale-105">
            <h3 className="text-2xl font-semibold text-gray-900 mb-4">
              Interested in Our Products?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Visit our store to see our full collection, try on different styles,
              and get personalized fitting assistance from our knowledgeable staff.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/about"
                className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
              >
                Store Locations
              </a>
              <a
                href="/contact"
                className="border-2 border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
              >
                Contact Us
              </a>
            </div>
          </div>
        </AnimatedSection>
      </div>
    </PageTransition>
  );
}

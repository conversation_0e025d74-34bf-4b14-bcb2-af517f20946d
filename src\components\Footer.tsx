import Link from 'next/link';
import { FaInstagram, FaFacebook } from 'react-icons/fa';

export default function Footer() {
  return (
    <footer className="bg-gray-800 text-white">
      <div className="max-w-7xl mx-auto py-5 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <h3 className="text-2xl font-bold mb-4">Utsav Footwear</h3>
            <p className="text-gray-300 mb-4">
              Your trusted destination for kolhapuri chappal&apos;s. We also offers comfortable, 
              stylish, and durable footwear for every occasion.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Follow us on social media</h4>
            <ul className="space-y-2">
              <li>
                <Link href="https://www.instagram.com/utsav.footwear?igsh=MXJtZjJvZzdodm01bg==" className="flex items-center gap-2 text-gray-300 hover:text-white transition-colors">
                  <FaInstagram className="text-pink-500" />
                  Instagram
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-8 pt-8 border-t border-gray-700">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-300 text-sm">
              © {new Date().getFullYear()} Utsav Footwear. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}

'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'

export default function DetailedLocation() {
    const details = [
        {
            Store: "Store Details:",
            address: "2992 'A' Prathmesh Plaza, Near Mahalaxmi Dharmashala, Tarabai Road, Kolhapur, Maharashtra 416012",
            hours: '9 AM - 10 PM',
            // phone: '(*************',
            mapLink: 'https://maps.app.goo.gl/qBjDJ4qGM2cLTv7C8' 
        },
        {
            Store: "Store Details:",
            address: 'Utsav Footwear, Chappal Line, Shivaji Chowk, Kolhapur, Maharashtra 416002',
            hours: '9 AM - 10 PM',
            // phone: '(*************',
            mapLink: 'https://maps.app.goo.gl/46VQbJgUnELNxKmv9' 
        },
        {
            Store: "Store Details:",
            address: "Gala No 1, 3144 'A', Kulkarni Apartment, Kanerkar Marg, Tarabai Road, Kolhapur, Maharashtra 416008",
            hours: '9 AM - 10 PM',
            // phone: '(*************',
            mapLink: 'https://maps.app.goo.gl/ggpwJAPgky9fNPDY7' 
        }
        ,
        {
            Store: "Godaam Details:",
            address: "'Abhijit Footwear', 83b Near Ram Medical, Subhash Nagar, Kolhapur 416008",
            hours: '9 AM - 6 PM',
            // phone: '(*************',
            mapLink: 'https://maps.app.goo.gl/o8RqCzmuYHgqwkuo9' 
        }
    ]

    return (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto p-4">
            {details.map((detail, index) => (
                <Link
                    href={detail.mapLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    key={index}
                >
                    <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                        className="bg-gray-100 rounded-2xl shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer h-full max-h-64 overflow-hidden hover:scale-102"
                    >
                        <p className="text-gray-700 mb-2">📍 <strong>{detail.Store}</strong></p>
                        <p className="text-gray-700 mb-2">{detail.address}</p>
                        <p className="text-gray-600 mb-1">🕒 <strong>Hours:</strong> {detail.hours}</p>
                        {/* <p className="text-gray-600">📞 <strong>Phone:</strong> {detail.phone}</p> */}
                        <p className="text-gray-400 opacity-70">🗺️ <strong>Map:</strong> Click for directions</p>
                    </motion.div>
                </Link>
            ))}
        </div>
    )
}

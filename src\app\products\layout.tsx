import { Metadata } from 'next';

export const metadata: Metadata = {
  title: "Kolhapuri Chappals Collection - Premium Handmade Footwear",
  description: "Browse our exclusive collection of authentic Kolhapuri chappals and traditional footwear. Handcrafted leather sandals for men, women, and children. Quality guaranteed.",
  keywords: "kolhapuri chappals collection, handmade footwear, leather sandals, traditional chappals, kolhapur footwear, authentic kolhapuri sandals, premium chappals",
  openGraph: {
    title: "Kolhapuri Chappals Collection - Utsav Footwear",
    description: "Browse our exclusive collection of authentic Kolhapuri chappals and traditional footwear. Handcrafted leather sandals for men, women, and children.",
    url: "https://utsavfootwear.com/products",
    images: [
      {
        url: "/kolhapuri1.jpg",
        width: 1200,
        height: 630,
        alt: "Kolhapuri Chappals Collection at Utsav Footwear",
      },
    ],
  },
  alternates: {
    canonical: "https://utsavfootwear.com/products",
  },
};

export default function ProductsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {/* Structured Data for Products Page */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            "name": "Kolhapuri Chappals Collection",
            "description": "Browse our exclusive collection of authentic Kolhapuri chappals and traditional footwear",
            "url": "https://utsavfootwear.com/products",
            "mainEntity": {
              "@type": "ItemList",
              "name": "Kolhapuri Chappals",
              "description": "Premium handcrafted Kolhapuri chappals and traditional footwear"
            },
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": "https://utsavfootwear.com"
                },
                {
                  "@type": "ListItem",
                  "position": 2,
                  "name": "Products",
                  "item": "https://utsavfootwear.com/products"
                }
              ]
            }
          })
        }}
      />
      {children}
    </>
  );
}

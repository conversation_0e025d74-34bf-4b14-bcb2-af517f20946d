import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedDatabase } from 'uf/lib/firebase-admin';
import { ref as dbRef, get } from 'firebase/database';

export interface Product {
  id?: string;
  name: string;
  caption: string;
  imageUrl: string;
  publicId?: string;
  createdAt?: number;
  updatedAt?: number;
}

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '12');

    // Validate parameters
    if (page < 1 || pageSize < 1 || pageSize > 100) {
      return NextResponse.json(
        { error: 'Invalid pagination parameters' },
        { status: 400 }
      );
    }

    // Get authenticated database reference
    const database = await getAuthenticatedDatabase();
    const productsRef = dbRef(database, 'products');

    // Fetch all products from Firebase
    const snapshot = await get(productsRef);
    
    if (!snapshot.exists()) {
      return NextResponse.json({
        products: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0
      });
    }

    const data = snapshot.val();
    const products: Product[] = [];

    // Convert Firebase data to array
    Object.keys(data).forEach(key => {
      products.push({
        id: key,
        ...data[key]
      });
    });

    // Sort products by creation date (newest first)
    products.sort((a, b) => {
      const aTime = a.createdAt || 0;
      const bTime = b.createdAt || 0;
      return bTime - aTime;
    });

    // Calculate pagination
    const total = products.length;
    const totalPages = Math.ceil(total / pageSize);
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedProducts = products.slice(start, end);

    return NextResponse.json({
      products: paginatedProducts,
      total,
      page,
      pageSize,
      totalPages
    });

  } catch (error) {
    console.error('Error fetching products:', error);
    
    // Return appropriate error response
    if (error instanceof Error) {
      return NextResponse.json(
        { error: 'Failed to fetch products', details: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET single product by ID
export async function POST(request: NextRequest) {
  try {
    const { productId } = await request.json();

    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }

    // Get authenticated database reference
    const database = await getAuthenticatedDatabase();
    const productRef = dbRef(database, `products/${productId}`);

    // Fetch the specific product
    const snapshot = await get(productRef);
    
    if (!snapshot.exists()) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    const product: Product = {
      id: productId,
      ...snapshot.val()
    };

    return NextResponse.json({ product });

  } catch (error) {
    console.error('Error fetching product:', error);
    
    return NextResponse.json(
      { error: 'Failed to fetch product' },
      { status: 500 }
    );
  }
}

// Client-side helper functions for Cloudinary operations
export async function uploadToCloudinary(file: File): Promise<{ url: string; publicId: string }> {
  try {
    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      throw new Error('File size too large. Maximum size is 10MB.');
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      throw new Error('Invalid file type. Only JPEG, PNG, and WebP images are allowed.');
    }

    // Step 1: Get signed upload parameters from our API
    const signatureResponse = await fetch('/api/upload-signature', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!signatureResponse.ok) {
      const errorData = await signatureResponse.json();
      console.error('Signature error:', errorData);
      throw new Error(`Failed to get upload signature: ${errorData.error || 'Unknown error'}`);
    }

    const signatureData = await signatureResponse.json();

    // Step 2: Upload directly to Cloudinary using signed parameters
    const formData = new FormData();
    formData.append('file', file);
    formData.append('signature', signatureData.signature);
    formData.append('timestamp', signatureData.timestamp.toString());
    formData.append('api_key', signatureData.api_key);
    formData.append('folder', signatureData.folder);

    const uploadResponse = await fetch(
      `https://api.cloudinary.com/v1_1/${signatureData.cloud_name}/image/upload`,
      {
        method: 'POST',
        body: formData,
        // Add timeout to prevent hanging requests
        signal: AbortSignal.timeout(30000), // 30 seconds timeout
      }
    );

    if (!uploadResponse.ok) {
      const errorData = await uploadResponse.json();
      console.error('Cloudinary upload error:', errorData);
      throw new Error(`Failed to upload image to Cloudinary: ${errorData.error?.message || 'Unknown error'}`);
    }

    const uploadData = await uploadResponse.json();
    return {
      url: uploadData.secure_url,
      publicId: uploadData.public_id,
    };
  } catch (error) {
    console.error('Error uploading to Cloudinary:', error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Failed to upload image');
  }
}

export async function deleteFromCloudinary(publicId: string): Promise<void> {
  try {
    const response = await fetch('/api/deleteFromCloudinary', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ publicId }),
    });

    if (!response.ok) {
      throw new Error('Failed to delete image from Cloudinary');
    }
  } catch (error) {
    console.error('Error deleting from Cloudinary:', error);
    throw new Error('Failed to delete image');
  }
}

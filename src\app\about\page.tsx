import PageTransition from 'uf/components/PageTransition';
import AnimatedSection from 'uf/components/AnimatedSection';
import { Metadata } from 'next';
import { breadcrumbSchema } from 'uf/lib/structuredData';

export const metadata: Metadata = {
  title: "About Utsav Footwear - Your Trusted Kolhapuri Chappal Store in Kolhapur",
  description: "Learn about Utsav Footwear's journey in providing premium Kolhapuri chappals in Kolhapur. Our story, commitment to quality, and dedication to traditional craftsmanship.",
  keywords: "about utsav footwear, kolhapuri chappal store kolhapur, traditional footwear, handmade chappals, footwear store history, quality commitment",
  openGraph: {
    title: "About Utsav Footwear - Your Trusted Kolhapuri Chappal Store",
    description: "Learn about Utsav Footwear's journey in providing premium Kolhapuri chappals in Kolhapur. Our story and commitment to quality.",
    url: "https://utsavfootwear.com/about",
    images: [
      {
        url: "/Ustav Logo-03.png",
        width: 1200,
        height: 630,
        alt: "About Utsav Footwear - Kolhapuri Chappal Store",
      },
    ],
  },
  alternates: {
    canonical: "https://utsavfootwear.com/about",
  },
};
import DetailedLocation from 'uf/components/DetailedLocation';

export default function About() {
  const aboutBreadcrumb = breadcrumbSchema([
    { name: "Home", url: "/" },
    { name: "About", url: "/about" }
  ]);

  return (
    <PageTransition className="min-h-screen">
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(aboutBreadcrumb) }}
      />

      <div className="relative bg-[url('/KCbg2.png')] bg-cover bg-center bg-no-repeat">
      {/* Black Overlay */}
      <div className="absolute inset-0 bg-black/60 z-0"></div>
      <div className="relative z-10 max-w-full max-h-full mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <AnimatedSection className="text-center mb-16" disableScrollAnimation={true}>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-100 mb-6 py-12">
            About Utsav Footwear
          </h1>
          <p className="text-xl text-green-300 max-w-3xl mx-auto">
            Discover the story behind our passion for quality footwear and our commitment
            to serving our community with the finest shoes.
          </p>
        </AnimatedSection>

        {/* Founder Story */}
        <AnimatedSection className="mb-16" delay={0.2}>
          <div className="bg-white rounded-lg shadow-lg p-8 md:p-12 transform transition-transform duration-300 hover:scale-102">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Story</h2>
            <div className="prose prose-lg max-w-none text-gray-700">
              <p className="mb-6">
                Founded with a vision to provide quality footwear to our community, Utsav Footwear
                has been serving customers with dedication and passion. Our journey began with a
                simple belief: everyone deserves comfortable, stylish, and durable shoes that
                enhance their daily lives.
              </p>
              <p className="mb-6">
                While we offer a wide variety of footwear, our heart and heritage lie in the
                craftsmanship of the iconic <strong>Kolhapuri chappal</strong>. Handcrafted by skilled
                artisans, each pair reflects generations of tradition, cultural pride, and
                unmatched durability. We take pride in preserving this timeless art form while
                blending it with modern designs to suit contemporary tastes.
              </p>
              <p className="mb-6">
                What started as a small family business has grown into a trusted name in footwear,
                known for our commitment to quality, customer service, and community values. We
                carefully curate our collection to ensure that every pair of shoes—especially our
                Kolhapuri chappals—meets our high standards for comfort, style, and longevity.
              </p>
              <p>
                At Utsav Footwear, we believe that the right pair of shoes can make all the
                difference in your day. Whether you&apos;re looking for traditional Kolhapuri chappals,
                formal shoes for work, comfortable sneakers for daily wear, or special occasion
                footwear, we&apos;re here to help you find the perfect fit.
              </p>
            </div>
          </div>
        </AnimatedSection>

        {/* Values */}
        <section className="mb-16 z-10">
          <h2 className="text-3xl font-bold text-blue-500 text-center mb-12">Our Values</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-3xl">🎯</span>
              </div>
              <h3 className="text-xl font-semibold mb-4">Quality First</h3>
              <p className="text-gray-300">
                We never compromise on quality. Every shoe in our collection is carefully 
                selected for its craftsmanship, materials, and durability.
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-3xl">🤝</span>
              </div>
              <h3 className="text-xl font-semibold mb-4">Customer Care</h3>
              <p className="text-gray-300">
                Your satisfaction is our priority. We provide personalized service and 
                expert advice to help you find the perfect footwear.
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-3xl">🏘️</span>
              </div>
              <h3 className="text-xl font-semibold mb-4">Community Focus</h3>
              <p className="text-gray-300">
                We&apos;re proud to be part of the local community and committed to serving
                our neighbors with integrity and care.
              </p>
            </div>
          </div>
        </section>
      </div>

      {/* Store Locations */}
      <section className='relative w-full h-full z-10'>
        <h2 className="text-3xl font-bold text-blue-500 text-center mb-12">Visit Our Stores</h2>
        <div className="bg-gray-50 rounded-lg p-8">
          <div className="text-center">
            <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-2xl">🏪</span>
            </div>
            <h3 className="text-2xl font-semibold mb-4 text-gray-700">Store Locations</h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              We have multiple convenient locations in kolhapur to serve you better. Each store is 
              staffed with knowledgeable team members ready to help you find the perfect fit.
            </p>
            <DetailedLocation />
          </div>
        </div>
      </section>
    </div>
    </PageTransition>
  );
}

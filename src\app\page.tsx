import Link from 'next/link';
import PageTransition from 'uf/components/PageTransition';
import AnimatedSection from 'uf/components/AnimatedSection';
import { Metadata } from 'next';
import { organizationSchema, localBusinessSchema, websiteSchema } from 'uf/lib/structuredData';

export const metadata: Metadata = {
  title: "Premium Kolhapuri Chappals in Kolhapur - Handmade Leather Sandals",
  description: "Discover authentic Kolhapuri chappals at Utsav Footwear in Kolhapur. Premium handmade leather sandals for men, women, and kids. Top quality traditional footwear with modern comfort.",
  keywords: "kolhapuri chappals kolhapur, handmade leather sandals, traditional footwear, kolhapuri chappal shop, premium sandals, authentic kolhapuri chappals, footwear kolhapur, leather chappals",
  openGraph: {
    title: "Premium Kolhapuri Chappals in Kolhapur - Utsav Footwear",
    description: "Discover authentic Kolhapuri chappals at Utsav Footwear in Kolhapur. Premium handmade leather sandals for men, women, and kids.",
    url: "https://utsavfootwear.com",
    images: [
      {
        url: "/kolhapuribg.png",
        width: 1200,
        height: 630,
        alt: "Utsav Footwear - Premium Kolhapuri Chappals in Kolhapur",
      },
    ],
  },
  alternates: {
    canonical: "https://utsavfootwear.com",
  },
};

export default function Home() {
  const combinedSchema = {
    "@context": "https://schema.org",
    "@graph": [
      organizationSchema,
      localBusinessSchema,
      websiteSchema
    ]
  };

  return (
    <PageTransition className="min-h-screen">
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(combinedSchema) }}
      />

      {/* Hero Section */}
      <section className="relative min-h-[80vh] bg-[url('/kolhapuribg.png')] bg-cover bg-center bg-no-repeat">
        <div 
          className="absolute inset-0 bg-gradient-to-b from-black/70 to-black/60 z-0"
          style={{
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundAttachment: 'scroll'
          }}
        ></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center py-20 md:py-32">
          <AnimatedSection delay={0.2} disableScrollAnimation={true}>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Welcome to Utsav Footwear
            </h1>
          </AnimatedSection>

          <AnimatedSection delay={0.4} disableScrollAnimation={true}>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              Your trusted destination for quality kolhapuri chappals in kolhapur.
              Discover our kolhapuri chappal collection and footwear&apos;s for every occasion.
            </p>
          </AnimatedSection>

          <AnimatedSection delay={0.6} disableScrollAnimation={true}>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/products"
                className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
              >
                View Products
              </Link>
              <Link
                href="/about"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
              >
                Learn More
              </Link>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50 bg-yellow-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <AnimatedSection className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Choose Utsav Footwear?</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We are committed to providing you with the best footwear experience
            </p>
          </AnimatedSection>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: "👟",
                title: "Quality Products",
                description: "We source only the finest materials to ensure durability and comfort"
              },
              {
                icon: "🏪",
                title: "Multiple Locations",
                description: "Visit our conveniently located stores for personalized service"
              },
              {
                icon: "💝",
                title: "Customer Satisfaction",
                description: "Your comfort and satisfaction are our top priorities"
              }
            ].map((feature, index) => (
              <AnimatedSection
                key={feature.title}
                className="text-center"
                delay={0.2 + index * 0.2}
                direction="up"
              >
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 transform transition-transform duration-300 hover:scale-110 hover:shadow-lg">
                  <span className="text-2xl">{feature.icon}</span>
                </div>
                <h3 className="text-xl font-semibold mb-2 text-green-900">{feature.title}</h3>
                <p className="text-black">
                  {feature.description}
                </p>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <AnimatedSection>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Ready to Find Your Perfect Pair?</h2>
            <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              Browse our collection or visit one of our stores to experience the Utsav Footwear difference
            </p>
          </AnimatedSection>

          <AnimatedSection delay={0.3}>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/products"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
              >
                Browse Products
              </Link>
              <Link
                href="/contact"
                className="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
              >
                Contact Us
              </Link>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </PageTransition>
  );
}

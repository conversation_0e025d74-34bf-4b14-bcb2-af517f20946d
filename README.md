# Utsav Footwear Website

A modern e-commerce website for Utsav Footwear built with Next.js, TypeScript, Tailwind CSS, and Firebase.

## Features

- **SSR-first architecture** with Next.js App Router
- **Responsive design** with Tailwind CSS
- **Firebase integration** for authentication, database, and storage
- **Admin dashboard** for photo management
- **Contact form** with EmailJS integration
- **TypeScript** for type safety
- **Modern UI/UX** with smooth animations
- **Comprehensive SEO optimization** with meta tags, structured data, and sitemaps
- **Performance optimized** with image optimization and caching
- **Security headers** and best practices implemented
- **Google Analytics** integration ready
- **PWA features** with manifest and service worker support

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS 4
- **Backend**: Firebase (Auth, Firestore, Storage)
- **Email**: EmailJS
- **Deployment**: Firebase Hosting (planned)

## Pages

- `/` - Home page with store introduction
- `/about` - Founder story and store locations
- `/products` - Product gallery from Firebase
- `/contact` - Contact form using EmailJS
- `/manage-photos/login` - Admin login
- `/manage-photos` - Admin dashboard for photo management

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Firebase project
- EmailJS account (for contact form)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd utsavfootwear
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

4. Configure Firebase:
   - Create a Firebase project at [Firebase Console](https://console.firebase.google.com)
   - Enable Authentication, Firestore, and Storage
   - Copy your Firebase config to `.env.local`

5. Configure EmailJS:
   - Create an account at [EmailJS](https://www.emailjs.com)
   - Set up a service and template
   - Add your EmailJS credentials to `.env.local`

6. Run the development server:
```bash
npm run dev
```

7. Open [http://localhost:3000](http://localhost:3000) in your browser

## Firebase Setup

### Authentication
- Enable Email/Password authentication
- Create an admin user for photo management

### Firestore Database
- Create a `products` collection
- Set up security rules (see Firebase documentation)

### Storage
- Create a `products` folder for images
- Configure storage rules for admin access

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── about/             # About page
│   ├── contact/           # Contact page
│   ├── manage-photos/     # Admin section
│   ├── products/          # Products page
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # Reusable components
│   ├── AuthGuard.tsx      # Authentication guard
│   ├── Footer.tsx         # Site footer
│   ├── Navbar.tsx         # Navigation bar
│   ├── PhotoUploadModal.tsx # Photo upload modal
│   └── ProductCard.tsx    # Product display card
└── lib/
    └── firebase.ts        # Firebase configuration
```

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Import Alias

The project uses `uf/*` as an import alias for the `src/` directory:

```typescript
import Navbar from 'uf/components/Navbar';
import { db } from 'uf/lib/firebase';
```

## Deployment

The project is configured for Firebase Hosting deployment. See Firebase documentation for deployment instructions.

## SEO Setup Instructions

### 1. Google Analytics Setup
1. Create a Google Analytics 4 property
2. Replace `G-XXXXXXXXXX` in `src/components/Analytics.tsx` with your actual measurement ID
3. The analytics will automatically track page views and custom events

### 2. Google Search Console
1. Add your website to Google Search Console
2. Replace `your-google-verification-code` in `src/app/layout.tsx` with your verification code
3. Submit your sitemap: `https://yourdomain.com/sitemap.xml`

### 3. Update Domain URLs
Replace `https://utsavfootwear.com` with your actual domain in:
- `src/app/sitemap.ts`
- `src/lib/structuredData.ts`
- All layout files with metadata

### 4. Business Information
Update the following in `src/lib/structuredData.ts`:
- Business address and contact information
- Phone number
- Opening hours
- Social media URLs

### 5. Images for SEO
Add these images to your `public` folder:
- `icon-192.png` (192x192 PWA icon)
- `icon-512.png` (512x512 PWA icon)
- `apple-touch-icon.png` (180x180 Apple touch icon)

### 6. Performance Monitoring
- Set up Google PageSpeed Insights monitoring
- Use Lighthouse for regular audits
- Monitor Core Web Vitals in Search Console

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is private and proprietary to Utsav Footwear.

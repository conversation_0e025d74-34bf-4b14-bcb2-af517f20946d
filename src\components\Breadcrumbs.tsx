import Link from 'next/link';

interface BreadcrumbItem {
  name: string;
  href: string;
  current?: boolean;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
}

export default function Breadcrumbs({ items }: BreadcrumbsProps) {
  return (
    <nav className="flex" aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {/* Home link */}
        <li>
          <Link
            href="/"
            className="text-gray-400 hover:text-gray-500 transition-colors duration-200"
            aria-label="Home"
          >
            🏠
          </Link>
        </li>

        {items.map((item) => (
          <li key={item.href} className="flex items-center">
            <span className="text-gray-300 mx-2">›</span>
            {item.current ? (
              <span className="text-gray-500 font-medium" aria-current="page">
                {item.name}
              </span>
            ) : (
              <Link
                href={item.href}
                className="text-gray-400 hover:text-gray-500 transition-colors duration-200"
              >
                {item.name}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}
